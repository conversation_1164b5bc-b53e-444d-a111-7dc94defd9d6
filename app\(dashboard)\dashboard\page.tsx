'use client'

import { useSession } from 'next-auth/react'
import { Loader2 } from 'lucide-react'

// Import role-specific dashboard components
import AdminDashboard from '@/components/dashboard/role-dashboards/admin-dashboard'
import ManagerDashboard from '@/components/dashboard/role-dashboards/manager-dashboard'
import ReceptionDashboard from '@/components/dashboard/role-dashboards/reception-dashboard'
import CashierDashboard from '@/components/dashboard/role-dashboards/cashier-dashboard'

export default function DashboardPage() {
  const { data: session, status } = useSession()

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (!session?.user?.role) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600">Unable to determine user role</p>
        </div>
      </div>
    )
  }

  // Route to appropriate dashboard based on user role
  const userRole = session.user.role

  switch (userRole) {
    case 'ADMIN':
      return <AdminDashboard />
    case 'MANAGER':
      return <ManagerDashboard />
    case 'RECEPTION':
      return <ReceptionDashboard />
    case 'CASHIER':
      return <CashierDashboard />
    default:
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <p className="text-red-600">Unknown user role: {userRole}</p>
          </div>
        </div>
      )
  }
}



