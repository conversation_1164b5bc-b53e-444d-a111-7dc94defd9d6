import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import { z } from 'zod'
import bcrypt from 'bcryptjs'

const userUpdateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  phone: z.string().min(9, 'Phone number must be at least 9 characters').optional(),
  email: z.string().email().optional(),
  role: z.enum(['ADMIN', 'MANAGER', 'RECEPTION', 'CASHIER']).optional(),
  branch: z.enum(['main', 'branch']).optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        phone: true,
        email: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        studentProfile: {
          select: {
            id: true,
            level: true,
            branch: true,
            status: true,
            emergencyContact: true,
            dateOfBirth: true,
            address: true,
          },
        },
        teacherProfile: {
          select: {
            id: true,
            subject: true,
            experience: true,
            salary: true,
            branch: true,
          },
        },
        _count: {
          select: {
            activityLogs: true,
          },
        },
      },
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    return NextResponse.json(user)
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only admins and managers can update users
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = userUpdateSchema.parse(body)

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
    })

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Check for duplicate phone if phone is being updated
    if (validatedData.phone && validatedData.phone !== existingUser.phone) {
      const phoneExists = await prisma.user.findUnique({
        where: { phone: validatedData.phone },
      })

      if (phoneExists) {
        return NextResponse.json(
          { error: 'User with this phone number already exists' },
          { status: 400 }
        )
      }
    }

    // Check for duplicate email if email is being updated
    if (validatedData.email && validatedData.email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email: validatedData.email },
      })

      if (emailExists) {
        return NextResponse.json(
          { error: 'User with this email already exists' },
          { status: 400 }
        )
      }
    }

    // Prepare update data
    const updatePayload: any = {}

    if (validatedData.name) updatePayload.name = validatedData.name
    if (validatedData.phone) updatePayload.phone = validatedData.phone
    if (validatedData.email !== undefined) updatePayload.email = validatedData.email
    if (validatedData.role) updatePayload.role = validatedData.role
    if (validatedData.branch) updatePayload.branch = validatedData.branch

    if (validatedData.password) {
      updatePayload.password = await bcrypt.hash(validatedData.password, 12)
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id },
      data: updatePayload,
      select: {
        id: true,
        name: true,
        phone: true,
        email: true,
        role: true,
        branch: true,
        createdAt: true,
        updatedAt: true,
        studentProfile: {
          select: {
            id: true,
            level: true,
            branch: true,
          },
        },
        teacherProfile: {
          select: {
            id: true,
            subject: true,
            experience: true,
            branch: true,
          },
        },
      },
    })

    // Log the activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: session.user.role as Role,
      action: 'UPDATE',
      resource: 'user',
      resourceId: updatedUser.id,
      details: {
        changes: validatedData,
        targetUserName: updatedUser.name,
        targetUserRole: updatedUser.role,
      },
      ipAddress: ActivityLogger.getIpAddress(request),
      userAgent: ActivityLogger.getUserAgent(request),
    })

    return NextResponse.json(updatedUser)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only admins can delete users
    if (!session.user.role || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { id } = await params

    // Prevent self-deletion
    if (id === session.user.id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      )
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
      include: {
        studentProfile: true,
        teacherProfile: true,
      }
    })

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Use transaction to handle cascading deletes properly
    await prisma.$transaction(async (tx) => {
      // If user is a student, delete related records first
      if (existingUser.studentProfile) {
        const studentId = existingUser.studentProfile.id

        // Delete payments
        await tx.payment.deleteMany({
          where: { studentId }
        })

        // Delete enrollments
        await tx.enrollment.deleteMany({
          where: { studentId }
        })

        // Delete attendance records
        await tx.attendance.deleteMany({
          where: { studentId }
        })

        // Delete assessments
        await tx.assessment.deleteMany({
          where: { studentId }
        })

        // Delete student profile
        await tx.student.delete({
          where: { id: studentId }
        })
      }

      // If user is a teacher, handle teacher-related records
      if (existingUser.teacherProfile) {
        const teacherId = existingUser.teacherProfile.id

        // Update groups to remove teacher assignment (set to null)
        await tx.group.updateMany({
          where: { teacherId },
          data: { teacherId: null as any }
        })

        // Delete teacher profile
        await tx.teacher.delete({
          where: { id: teacherId }
        })
      }

      // Delete activity logs
      await tx.activityLog.deleteMany({
        where: { userId: id }
      })

      // Delete call records
      await tx.callRecord.deleteMany({
        where: { userId: id }
      })

      // Finally, delete the user
      await tx.user.delete({
        where: { id }
      })
    })

    // Log the activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: session.user.role as Role,
      action: 'DELETE',
      resource: 'user',
      resourceId: id,
      details: {
        deletedUserName: existingUser.name,
        deletedUserRole: existingUser.role,
        hadStudentProfile: !!existingUser.studentProfile,
        hadTeacherProfile: !!existingUser.teacherProfile,
      },
      ipAddress: ActivityLogger.getIpAddress(request),
      userAgent: ActivityLogger.getUserAgent(request),
    })

    return NextResponse.json({ message: 'User deleted successfully' })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
