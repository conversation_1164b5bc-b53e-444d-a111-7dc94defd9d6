import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'

// Define protected routes and their required roles
const protectedRoutes = {
  '/dashboard': ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'STUDENT', 'ACADEMIC_MANAGER'],
  '/dashboard/analytics': ['ADMIN'], // ADMIN ONLY for financial data
  '/dashboard/users': ['ADMIN'],
  '/dashboard/teachers': ['ADMIN', 'MANAGER'],
  '/dashboard/students': ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER'], // Added CASHIER
  '/dashboard/groups': ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION'],
  '/dashboard/enrollments': ['ADMIN', 'MANAGER', 'RECEPTION'],
  '/dashboard/payments': ['ADMIN', 'CASHIER'], // ADMIN and CASHIER only
  '/dashboard/classes': ['ADMIN', 'MANAGER', 'TEACHER'],
  '/dashboard/leads': ['ADMIN', 'MANAGER', 'RECEPTION'],
  '/dashboard/cabinets': ['ADMIN', 'MANAGER'],
  '/dashboard/communication': ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION'],
}

// API routes that require authentication
const protectedApiRoutes = {
  '/api/analytics': ['ADMIN'], // ADMIN ONLY for financial analytics
  '/api/reports': ['ADMIN'], // ADMIN ONLY for financial reports
  '/api/users': ['ADMIN'],
  '/api/teachers': ['ADMIN', 'MANAGER'],
  '/api/students': ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'STUDENT', 'ACADEMIC_MANAGER'], // Added CASHIER, STUDENT, ACADEMIC_MANAGER for attendance access
  '/api/groups': ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION'],
  '/api/enrollments': ['ADMIN', 'MANAGER', 'RECEPTION'],
  '/api/payments': ['ADMIN', 'CASHIER'], // ADMIN and CASHIER only
  '/api/leads': ['ADMIN', 'MANAGER', 'RECEPTION'],
  '/api/cabinets': ['ADMIN', 'MANAGER'],
  '/api/courses': ['ADMIN', 'MANAGER'],
}

// Public routes that don't require authentication
const publicRoutes = [
  '/',
  '/auth/signin',
  '/auth/signup',
  '/auth/error',
  '/api/auth',
  '/api/health', // Allow health check endpoint
  '/api/leads', // Allow public lead submission
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/static') ||
    pathname.includes('.') ||
    pathname.startsWith('/favicon')
  ) {
    return NextResponse.next()
  }

  // Debug logging for API routes (development only)
  if (process.env.NODE_ENV === 'development') {
    if (pathname.startsWith('/api/students')) {
      console.log('🔍 Middleware: Processing /api/students request')
    }

    if (pathname.startsWith('/api/teachers')) {
      console.log('🔍 Middleware: Processing /api/teachers request')
    }
  }

  // Check if route is public
  const isPublicRoute = publicRoutes.some(route => {
    if (route === pathname) return true
    if (route.endsWith('*') && pathname.startsWith(route.slice(0, -1))) return true
    return false
  })

  // Allow public routes
  if (isPublicRoute) {
    return NextResponse.next()
  }

  // Get the token from the request
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
    cookieName: 'next-auth.session-token',
  })

  // Debug logging for API routes
  if (pathname.startsWith('/api/students')) {
    console.log('🔍 Middleware: Token retrieved:', !!token)
    console.log('🔍 Middleware: Token details:', token ? { sub: token.sub, role: token.role, branch: token.branch } : 'No token')
  }

  // Redirect to signin if no token
  if (!token) {
    if (process.env.NODE_ENV === 'development' && (pathname.startsWith('/api/students') || pathname.startsWith('/api/teachers'))) {
      console.log('🔍 Middleware: No token found, redirecting to signin')
    }
    const signInUrl = new URL('/auth/signin', request.url)
    signInUrl.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(signInUrl)
  }

  // Check role-based access for protected routes
  const userRole = token.role as string

  // Debug logging for API routes (development only)
  if (process.env.NODE_ENV === 'development') {
    if (pathname.startsWith('/api/students') || pathname.startsWith('/api/teachers')) {
      console.log('🔍 Middleware: Starting route checks for', pathname)
      console.log('🔍 Middleware: User role:', userRole)
    }
  }

  // Check dashboard routes
  for (const [route, allowedRoles] of Object.entries(protectedRoutes)) {
    if (pathname.startsWith(route)) {
      if (pathname.startsWith('/api/students')) {
        console.log('🔍 Middleware: Matched dashboard route:', route)
      }
      if (!allowedRoles.includes(userRole)) {
        return NextResponse.redirect(new URL('/dashboard/unauthorized', request.url))
      }
      break
    }
  }

  // Debug logging for API routes
  if (pathname.startsWith('/api/students')) {
    console.log('🔍 Middleware: Finished dashboard route checks, proceeding to API checks')
  }

  // Check API routes
  for (const [route, allowedRoles] of Object.entries(protectedApiRoutes)) {
    if (pathname.startsWith(route)) {
      if (process.env.NODE_ENV === 'development' && (pathname.startsWith('/api/students') || pathname.startsWith('/api/teachers'))) {
        console.log('🔍 Middleware: Checking API route access')
        console.log('🔍 Middleware: Route:', route)
        console.log('🔍 Middleware: Allowed roles:', allowedRoles)
        console.log('🔍 Middleware: User role:', userRole)
        console.log('🔍 Middleware: Role allowed?', allowedRoles.includes(userRole))
      }

      if (!allowedRoles.includes(userRole)) {
        if (process.env.NODE_ENV === 'development' && (pathname.startsWith('/api/students') || pathname.startsWith('/api/teachers'))) {
          console.log('🔍 Middleware: Access denied - returning 403')
        }
        return NextResponse.json(
          { error: 'Unauthorized access' },
          { status: 403 }
        )
      }
      break
    }
  }

  // Special handling for student access
  if (userRole === 'STUDENT') {
    // Students can only access their own data
    const userId = token.sub

    // Allow access to student dashboard
    if (pathname.startsWith('/dashboard/student')) {
      return NextResponse.next()
    }

    // Restrict access to other dashboard routes
    if (pathname.startsWith('/dashboard') && pathname !== '/dashboard') {
      return NextResponse.redirect(new URL('/dashboard/student', request.url))
    }
  }

  // Special handling for academic manager access
  if (userRole === 'ACADEMIC_MANAGER') {
    // Academic managers have access to assessments and test statistics
    const allowedPaths = ['/dashboard', '/dashboard/assessments', '/dashboard/students']
    const isAllowed = allowedPaths.some(path => pathname.startsWith(path))

    if (!isAllowed && pathname.startsWith('/dashboard')) {
      return NextResponse.redirect(new URL('/dashboard/assessments', request.url))
    }
  }

  // Teacher-specific restrictions
  if (userRole === 'TEACHER') {
    // Teachers can access their assigned groups and students
    if (pathname.startsWith('/dashboard/teacher')) {
      return NextResponse.next()
    }
  }

  // Reception-specific restrictions (only for dashboard routes, not API routes)
  if (userRole === 'RECEPTION' && pathname.startsWith('/dashboard')) {
    // Reception can access leads, students, enrollments, and groups
    const allowedPaths = ['/dashboard', '/dashboard/leads', '/dashboard/students', '/dashboard/enrollments', '/dashboard/groups']
    if (!allowedPaths.some(path => pathname.startsWith(path))) {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
  }

  // Cashier-specific restrictions (only for dashboard routes, not API routes)
  if (userRole === 'CASHIER' && pathname.startsWith('/dashboard')) {
    // Cashiers can ONLY access payments and basic student info - NO financial analytics
    const allowedPaths = ['/dashboard', '/dashboard/payments', '/dashboard/students']
    if (!allowedPaths.some(path => pathname.startsWith(path))) {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }

    // Block access to any financial analytics or reports
    if (pathname.includes('/analytics') || pathname.includes('/reports')) {
      return NextResponse.redirect(new URL('/dashboard/unauthorized', request.url))
    }
  }

  // Debug logging for API routes (development only)
  if (process.env.NODE_ENV === 'development' && (pathname.startsWith('/api/students') || pathname.startsWith('/api/teachers'))) {
    console.log('🔍 Middleware: Allowing request to continue to API handler')
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)',
  ],
}
