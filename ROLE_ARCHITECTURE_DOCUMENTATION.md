# 🏗️ User Role Architecture & Database Interaction Logic

## 📋 **Overview**

The Innovative Centre CRM implements a comprehensive role-based access control (RBAC) system with branch-based data isolation. This document outlines how user roles interact with the platform and database.

## 🎭 **Role Hierarchy & Definitions**

### **1. ADMIN** 
- **Purpose**: System administration and financial oversight
- **Access Level**: Full system access
- **Branch Access**: Can switch between branches and view all data
- **Special Privileges**: User management, system configuration, financial data

### **2. CASHIER**
- **Purpose**: Payment processing and financial transactions
- **Access Level**: Payment-focused access
- **Branch Access**: Restricted to assigned branch
- **Special Privileges**: Payment creation/modification, student payment history

### **3. RECEPTION**
- **Purpose**: Lead management and student enrollment
- **Access Level**: Customer-facing operations
- **Branch Access**: Restricted to assigned branch
- **Special Privileges**: Lead management, student enrollment, basic student data

### **4. MANAGER**
- **Purpose**: Operational oversight and management
- **Access Level**: Operational data (no financial)
- **Branch Access**: Restricted to assigned branch
- **Special Privileges**: Teacher management, course management, operational reports

### **5. TEACHER**
- **Purpose**: Group instruction and student progress tracking
- **Access Level**: Own groups and students only
- **Branch Access**: Restricted to assigned branch + own groups
- **Special Privileges**: Assessment creation, student progress tracking

### **6. ACADEMIC_MANAGER**
- **Purpose**: Academic oversight and curriculum management
- **Access Level**: Academic data and assessments
- **Branch Access**: Restricted to assigned branch
- **Special Privileges**: Assessment management, academic reporting

### **7. STUDENT**
- **Purpose**: Personal learning dashboard
- **Access Level**: Own data only
- **Branch Access**: Own profile only
- **Special Privileges**: Personal progress tracking, payment history

## 🔐 **Security Architecture**

### **Authentication Flow**
```mermaid
graph TD
    A[User Login] --> B[NextAuth Session]
    B --> C[Role & Branch Assignment]
    C --> D[API Request]
    D --> E[Session Validation]
    E --> F[Role Authorization]
    F --> G[Branch Filtering]
    G --> H[Database Query]
    H --> I[Data Sanitization]
    I --> J[Response to Client]
```

### **API Security Layers**

#### **Layer 1: Authentication**
```typescript
const session = await getServerSession(authOptions)
if (!session?.user) {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
}
```

#### **Layer 2: Role-Based Authorization**
```typescript
const allowedRoles = ['ADMIN', 'MANAGER', 'RECEPTION']
if (!allowedRoles.includes(session.user.role)) {
  return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
}
```

#### **Layer 3: Branch-Based Data Filtering**
```typescript
const userRole = session.user.role
const userBranch = session.user.branch
let branchFilter: string

if (userRole === 'ADMIN') {
  branchFilter = requestedBranch || userBranch || 'main'
} else {
  branchFilter = userBranch || 'main'
}
```

#### **Layer 4: Data Sanitization**
```typescript
if (userRole !== 'ADMIN') {
  delete response.financialData
  delete response.systemLogs
}
```

## 📊 **Data Access Matrix**

| Resource | ADMIN | CASHIER | RECEPTION | MANAGER | TEACHER | ACADEMIC_MANAGER | STUDENT |
|----------|-------|---------|-----------|---------|---------|------------------|---------|
| **Users** | CRUD | - | - | - | - | - | Read Own |
| **Students** | CRUD | Read Payment | CRUD | Read | Read Groups | Read | Read Own |
| **Teachers** | CRUD | - | - | CRUD | - | - | - |
| **Groups** | CRUD | - | Read | CRUD | CRUD Own | Read | Read Own |
| **Courses** | CRUD | - | - | CRUD | - | - | - |
| **Leads** | CRUD | - | CRUD | Read | - | - | - |
| **Payments** | CRUD | CRUD | - | - | - | - | Read Own |
| **Assessments** | CRUD | - | - | Read | CRUD | CRUD | Read Own |
| **Enrollments** | CRUD | - | CRUD | Read | - | - | Read Own |
| **Financial Reports** | Read | Read Limited | - | - | - | - | - |
| **System Logs** | Read | - | - | - | - | - | - |

## 🌐 **Branch-Based Data Isolation**

### **Branch Assignment Logic**
- **Database Storage**: All entities store branch as ID (`"main"`, `"branch"`)
- **User Assignment**: Each user assigned to specific branch in profile
- **Admin Override**: ADMIN role can switch branches via UI
- **Data Filtering**: All queries automatically filtered by accessible branch(es)

### **Branch Context Implementation**
```typescript
// Frontend Branch Context
const branches = [
  { id: 'main', name: 'Main Branch' },
  { id: 'branch', name: 'Branch' }
]

// API Branch Filtering
const where = {
  branch: userRole === 'ADMIN' ? requestedBranch : userBranch,
  // ... other filters
}
```

## 🔄 **Database Interaction Patterns**

### **Pattern 1: Direct Entity Access**
```typescript
// For entities with direct branch field
const students = await prisma.student.findMany({
  where: { branch: branchFilter }
})
```

### **Pattern 2: Relationship-Based Access**
```typescript
// For entities accessed through relationships
const payments = await prisma.payment.findMany({
  where: { 
    student: { branch: branchFilter }
  }
})
```

### **Pattern 3: Role-Specific Filtering**
```typescript
// For teacher-specific access
const groups = await prisma.group.findMany({
  where: {
    branch: branchFilter,
    teacherId: userRole === 'TEACHER' ? userId : undefined
  }
})
```

### **Pattern 4: Multi-Level Filtering**
```typescript
// For complex relationships
const enrollments = await prisma.enrollment.findMany({
  where: {
    AND: [
      { student: { branch: branchFilter } },
      { group: { branch: branchFilter } }
    ]
  }
})
```

## 📱 **Frontend Role-Based Rendering**

### **Navigation Filtering**
```typescript
const getNavigationForRole = (role: string) => {
  const navigation = [
    {
      name: 'Dashboard',
      roles: ['ALL'],
      href: '/dashboard'
    },
    {
      name: 'Financial Management',
      roles: ['ADMIN', 'CASHIER'],
      href: '/payments'
    },
    {
      name: 'User Management',
      roles: ['ADMIN'],
      href: '/users'
    }
  ]
  
  return navigation.filter(item => 
    item.roles.includes('ALL') || item.roles.includes(role)
  )
}
```

### **Component-Level Security**
```typescript
// Conditional rendering based on role
{session?.user?.role === 'ADMIN' && (
  <AdminOnlyComponent />
)}

{['ADMIN', 'CASHIER'].includes(session?.user?.role) && (
  <FinancialDataComponent />
)}
```

## 🎯 **Role-Specific Dashboard Logic**

Each role has a dedicated dashboard API endpoint that returns role-appropriate data:

### **Admin Dashboard** (`/api/dashboard/admin-stats`)
- Complete system overview
- Financial metrics
- All branch data
- System health indicators

### **Cashier Dashboard** (`/api/dashboard/cashier-stats`)
- Payment statistics
- Revenue metrics (branch-specific)
- Pending payments
- Recent transactions

### **Reception Dashboard** (`/api/dashboard/reception-stats`)
- Lead statistics
- New inquiries
- Student enrollment metrics
- Recent activities

### **Manager Dashboard** (`/api/dashboard/manager-stats`)
- Operational metrics
- Teacher performance
- Group utilization
- Resource allocation

### **Teacher Dashboard** (`/api/dashboard/teacher-stats`)
- Own group statistics
- Student progress
- Assessment results
- Schedule overview

### **Academic Manager Dashboard** (`/api/dashboard/academic-manager-stats`)
- Academic performance metrics
- Assessment statistics
- Curriculum progress
- Student achievements

## 🔧 **Implementation Best Practices**

1. **Always Authenticate**: Every API endpoint must validate session
2. **Role-Based Authorization**: Check user role before data access
3. **Branch Filtering**: Apply branch filters for data isolation
4. **Data Sanitization**: Remove sensitive data based on role
5. **Consistent Error Handling**: Return appropriate HTTP status codes
6. **Activity Logging**: Log all user actions for audit trail
7. **Frontend Security**: Hide UI elements based on role permissions

This architecture ensures secure, scalable, and maintainable role-based access control throughout the CRM system.
