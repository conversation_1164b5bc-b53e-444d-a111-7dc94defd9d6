import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const teacherSchema = z.object({
  userId: z.string(),
  subject: z.string().min(1),
  experience: z.number().min(0).optional(),
  branch: z.string().min(1),
  photoUrl: z.string().optional(),
  tier: z.enum(['A_LEVEL', 'B_LEVEL', 'C_LEVEL', 'NEW']).default('NEW'),
})

export async function GET(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Teachers API: Starting GET request')
    }

    const session = await getServerSession(authOptions)

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Teachers API: Session:', session?.user ? { id: session.user.id, role: session.user.role, name: session.user.name } : 'No session')
    }

    if (!session?.user) {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Teachers API: No session found, returning 401')
      }
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only ADMIN and MANAGER can view teachers list
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Teachers API: Role not allowed:', session.user.role)
      }
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Teachers API: Role check passed, proceeding with query')
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const subject = searchParams.get('subject')
    const requestedBranch = searchParams.get('branch')

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Teachers API: Query params:', { page, limit, search, subject, requestedBranch })
    }

    // Determine branch based on user role
    const userRole = (session.user as any).role
    const userBranch = (session.user as any).branch
    let branchFilter: string

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Teachers API: User details:', { userRole, userBranch })
    }

    if (userRole === 'ADMIN') {
      // ADMIN can view any branch or all branches
      branchFilter = requestedBranch || userBranch || 'main'
    } else {
      // Non-admin users can only see their assigned branch
      branchFilter = userBranch || 'main'
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Teachers API: Branch filter determined:', branchFilter)
    }

    const where: any = {
      branch: branchFilter
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Teachers API: Where clause:', where)
    }

    if (search) {
      where.OR = [
        { user: { name: { contains: search, mode: 'insensitive' } } },
        { user: { phone: { contains: search } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
        { subject: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (subject) {
      where.subject = { contains: subject, mode: 'insensitive' }
    }

    const [teachers, total] = await Promise.all([
      prisma.teacher.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
              role: true,
              createdAt: true,
            },
          },
          groups: {
            include: {
              course: {
                select: {
                  id: true,
                  name: true,
                  level: true,
                  price: true,
                },
              },
              _count: {
                select: {
                  enrollments: true,
                  currentStudents: true,
                },
              },
              currentStudents: {
                select: {
                  id: true,
                  user: {
                    select: {
                      name: true,
                    },
                  },
                  status: true,
                },
                take: 5, // Limit to first 5 students for performance
              },
            },
          },
          classes: {
            include: {
              group: {
                select: {
                  name: true,
                },
              },
            },
            orderBy: { date: 'desc' },
            take: 5,
          },
          _count: {
            select: {
              groups: true,
              classes: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.teacher.count({ where }),
    ])

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Teachers API: Query results:', { teachersCount: teachers.length, total })
      console.log('🔍 Teachers API: First teacher (if any):', teachers[0] ? { id: teachers[0].id, branch: teachers[0].branch, userName: teachers[0].user?.name } : 'No teachers found')
    }

    return NextResponse.json({
      teachers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching teachers:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only ADMIN and MANAGER can create teachers
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = teacherSchema.parse(body)

    // Check if user exists and has TEACHER role
    const user = await prisma.user.findUnique({
      where: { id: validatedData.userId },
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 400 }
      )
    }

    if (user.role !== 'TEACHER') {
      return NextResponse.json(
        { error: 'User must have TEACHER role' },
        { status: 400 }
      )
    }

    // Check if teacher profile already exists for this user
    const existingTeacher = await prisma.teacher.findUnique({
      where: { userId: validatedData.userId },
    })

    let teacher
    if (existingTeacher) {
      // Update existing teacher profile with new data
      teacher = await prisma.teacher.update({
        where: { userId: validatedData.userId },
        data: {
          subject: validatedData.subject,
          experience: validatedData.experience,
          branch: validatedData.branch,
          tier: validatedData.tier,
          photoUrl: validatedData.photoUrl,
          updatedAt: new Date(),
        },
      })
    } else {
      // Create new teacher profile
      teacher = await prisma.teacher.create({
        data: validatedData,
      })
    }

    // Fetch the complete teacher data with relationships
    const completeTeacher = await prisma.teacher.findUnique({
      where: { id: teacher.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
            role: true,
          },
        },
        _count: {
          select: {
            groups: true,
            classes: true,
          },
        },
      },
    })

    return NextResponse.json(completeTeacher, { status: existingTeacher ? 200 : 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating teacher:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
