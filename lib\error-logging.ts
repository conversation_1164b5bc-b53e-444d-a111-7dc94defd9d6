import { prisma } from '@/lib/prisma'

export interface ErrorLogEntry {
  id: string
  timestamp: Date
  level: 'error' | 'warn' | 'info' | 'debug'
  message: string
  stack?: string
  userId?: string
  context?: Record<string, any>
}

export interface SecurityEvent {
  level: 'low' | 'medium' | 'high' | 'critical'
  type: string
  details: Record<string, any>
  timestamp: Date
}

class ErrorLogger {
  private isDevelopment = process.env.NODE_ENV === 'development'

  /**
   * Log an error with context information
   */
  logError(
    error: Error | string,
    userId?: string,
    context?: Record<string, any>
  ): string {
    const errorId = this.generateErrorId()
    const timestamp = new Date()
    
    const logEntry: ErrorLogEntry = {
      id: errorId,
      timestamp,
      level: 'error',
      message: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      userId,
      context
    }

    // Log to console in development
    if (this.isDevelopment) {
      console.error('Error logged:', {
        id: errorId,
        message: logEntry.message,
        stack: logEntry.stack,
        userId,
        context
      })
    }

    // In production, you might want to send to external logging service
    // For now, we'll just store basic info
    this.storeErrorLog(logEntry).catch(err => {
      if (this.isDevelopment) {
        console.error('Failed to store error log:', err)
      }
    })

    return errorId
  }

  /**
   * Log a security event
   */
  logSecurityEvent(
    level: 'low' | 'medium' | 'high' | 'critical',
    type: string,
    details: Record<string, any>
  ): void {
    const event: SecurityEvent = {
      level,
      type,
      details,
      timestamp: new Date()
    }

    // Log to console in development
    if (this.isDevelopment) {
      console.warn('Security event:', event)
    }

    // Store security event
    this.storeSecurityEvent(event).catch(err => {
      if (this.isDevelopment) {
        console.error('Failed to store security event:', err)
      }
    })
  }

  /**
   * Store error log in database (simplified version)
   */
  private async storeErrorLog(logEntry: ErrorLogEntry): Promise<void> {
    try {
      // For now, we'll create a simple activity log entry
      // In a full implementation, you'd have a dedicated error_logs table
      await prisma.activityLog.create({
        data: {
          userId: logEntry.userId || 'system',
          userRole: 'ADMIN', // Default role for system errors
          action: 'ERROR',
          resource: 'system',
          details: {
            errorId: logEntry.id,
            message: logEntry.message,
            level: logEntry.level,
            context: logEntry.context
          }
        }
      })
    } catch (error) {
      // Fail silently to avoid infinite error loops
      if (this.isDevelopment) {
        console.error('Failed to store error in database:', error)
      }
    }
  }

  /**
   * Store security event in database
   */
  private async storeSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      await prisma.activityLog.create({
        data: {
          userId: 'system',
          userRole: 'ADMIN', // Default role for security events
          action: 'SECURITY_EVENT',
          resource: 'security',
          details: {
            level: event.level,
            type: event.type,
            details: event.details
          }
        }
      })
    } catch (error) {
      // Fail silently to avoid infinite error loops
      if (this.isDevelopment) {
        console.error('Failed to store security event in database:', error)
      }
    }
  }

  /**
   * Generate a unique error ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Get error statistics (for monitoring)
   */
  async getErrorStats(timeframe: 'hour' | 'day' | 'week' = 'day'): Promise<{
    total: number
    byLevel: Record<string, number>
    recent: ErrorLogEntry[]
  }> {
    const now = new Date()
    const timeframeMs = {
      hour: 60 * 60 * 1000,
      day: 24 * 60 * 60 * 1000,
      week: 7 * 24 * 60 * 60 * 1000
    }
    
    const since = new Date(now.getTime() - timeframeMs[timeframe])

    try {
      const logs = await prisma.activityLog.findMany({
        where: {
          action: 'ERROR',
          createdAt: {
            gte: since
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 50
      })

      const total = logs.length
      const byLevel: Record<string, number> = {}
      
      logs.forEach(log => {
        const level = (log.details as any)?.level || 'unknown'
        byLevel[level] = (byLevel[level] || 0) + 1
      })

      const recent: ErrorLogEntry[] = logs.slice(0, 10).map(log => ({
        id: (log.details as any)?.errorId || log.id,
        timestamp: log.createdAt,
        level: (log.details as any)?.level || 'error',
        message: (log.details as any)?.message || 'Unknown error',
        userId: log.userId === 'system' ? undefined : log.userId,
        context: (log.details as any)?.context
      }))

      return { total, byLevel, recent }
    } catch (error) {
      if (this.isDevelopment) {
        console.error('Failed to get error stats:', error)
      }
      return { total: 0, byLevel: {}, recent: [] }
    }
  }
}

// Export singleton instance
export const errorLogger = new ErrorLogger()

// Export class for testing
export { ErrorLogger }
