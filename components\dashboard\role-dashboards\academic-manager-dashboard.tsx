'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/card'
import { StatsGrid } from '@/components/stats-grid'
import {
  Users,
  GraduationCap,
  TrendingUp,
  TrendingDown,
  Calendar,
  CheckCircle,
  ArrowUpRight,
  BookOpen,
  Loader2,
  ClipboardCheck,
  Award,
  Target,
  BarChart3
} from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'
import { useDashboardStore } from '@/lib/stores/dashboard-store'
import { Button } from '@/components/button'
import Link from 'next/link'

interface AcademicManagerDashboardStats {
  totalStudents: { count: number; growth: number }
  totalAssessments: { count: number; thisMonth: number }
  averageScore: { score: number; trend: number }
  completionRate: { rate: number; trend: number }
  recentAssessments: Array<{ studentName: string; testName: string; score: number; date: string; passed: boolean }>
  upcomingTests: Array<{ testName: string; groupName: string; date: string; studentsCount: number }>
  performanceByLevel: Array<{ level: string; averageScore: number; studentCount: number }>
}

export default function AcademicManagerDashboard() {
  const { currentBranch } = useBranch()
  const { refreshData } = useDashboardStore()
  const [stats, setStats] = useState<AcademicManagerDashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchAcademicManagerStats()
    refreshData()
  }, [])

  const fetchAcademicManagerStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/dashboard/academic-manager-stats')
      if (!response.ok) {
        throw new Error('Failed to fetch academic manager dashboard stats')
      }
      const data = await response.json()
      setStats(data)
      setError(null)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching academic manager dashboard stats:', error)
      }
      setError('Failed to load academic manager dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchAcademicManagerStats}>Try Again</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 tracking-tight">
            <Target className="inline h-8 w-8 mr-3 text-purple-600" />
            Academic Manager Dashboard - {currentBranch.name}
          </h1>
          <p className="text-gray-600 mt-1">Academic oversight and management tools.</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            className="shadow-sm"
            onClick={() => {
              fetchAcademicManagerStats()
              refreshData()
            }}
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            Refresh Data
          </Button>
          <Link href="/dashboard/students">
            <Button className="shadow-sm">
              <Users className="h-4 w-4 mr-2" />
              Manage Students
            </Button>
          </Link>
        </div>
      </div>

      {/* Academic Manager Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Total Students</CardTitle>
              <div className="h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-blue-700">{formatNumber(stats?.totalStudents.count || 0)}</div>
            <div className={`kpi-change mt-2 ${(stats?.totalStudents.growth ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(stats?.totalStudents.growth ?? 0) >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              <span>{(stats?.totalStudents.growth ?? 0) >= 0 ? '+' : ''}{stats?.totalStudents.growth || 0}% growth</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Assessments</CardTitle>
              <div className="h-10 w-10 rounded-full bg-green-50 flex items-center justify-center">
                <ClipboardCheck className="h-5 w-5 text-green-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-green-700">{formatNumber(stats?.totalAssessments.thisMonth || 0)}</div>
            <div className="kpi-change mt-2 text-green-600">
              <ClipboardCheck className="h-4 w-4" />
              <span>This month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Average Score</CardTitle>
              <div className="h-10 w-10 rounded-full bg-purple-50 flex items-center justify-center">
                <Award className="h-5 w-5 text-purple-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-purple-700">{stats?.averageScore.score || 0}%</div>
            <div className={`kpi-change mt-2 ${(stats?.averageScore.trend ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(stats?.averageScore.trend ?? 0) >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              <span>{(stats?.averageScore.trend ?? 0) >= 0 ? '+' : ''}{stats?.averageScore.trend || 0}% trend</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Completion Rate</CardTitle>
              <div className="h-10 w-10 rounded-full bg-orange-50 flex items-center justify-center">
                <Target className="h-5 w-5 text-orange-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-orange-700">{stats?.completionRate.rate || 0}%</div>
            <div className={`kpi-change mt-2 ${(stats?.completionRate.trend ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(stats?.completionRate.trend ?? 0) >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              <span>{(stats?.completionRate.trend ?? 0) >= 0 ? '+' : ''}{stats?.completionRate.trend || 0}% trend</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Academic Manager Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <CardTitle className="text-lg font-semibold text-gray-900">Quick Actions</CardTitle>
            <CardDescription>Academic management tools</CardDescription>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-3">
              <Link href="/dashboard/students">
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  Manage Students
                </Button>
              </Link>
              <Link href="/dashboard/groups">
                <Button variant="outline" className="w-full justify-start">
                  <GraduationCap className="h-4 w-4 mr-2" />
                  Manage Groups
                </Button>
              </Link>
              <Button variant="outline" className="w-full justify-start" disabled>
                <BarChart3 className="h-4 w-4 mr-2" />
                Performance Reports
              </Button>
              <Button variant="outline" className="w-full justify-start" disabled>
                <Award className="h-4 w-4 mr-2" />
                Grade Analytics
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Recent Activities</CardTitle>
                <CardDescription className="mt-1">Latest academic activities</CardDescription>
              </div>
              <Link href="/dashboard/students">
                <Button variant="ghost" size="sm" className="text-sm">
                  <ArrowUpRight className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              <div className="text-center py-8 text-gray-500">
                <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Academic activities will be displayed here</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Upcoming Tests</CardTitle>
                <CardDescription className="mt-1">Scheduled assessments</CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="text-sm">
                <Calendar className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {stats?.upcomingTests && stats.upcomingTests.length > 0 ? (
                stats.upcomingTests.slice(0, 3).map((test, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div>
                      <p className="font-medium text-gray-900">{test.testName}</p>
                      <p className="text-sm text-gray-600">{test.groupName}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-blue-600">{test.date}</p>
                      <p className="text-sm text-gray-500">{test.studentsCount} students</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No upcoming tests</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
