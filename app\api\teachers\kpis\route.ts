import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only allow certain roles to view teacher KPIs
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get all teachers with their groups and student payments
    const teachers = await prisma.teacher.findMany({
      include: {
        user: {
          select: {
            name: true,
          },
        },
        groups: {
          include: {
            enrollments: true,
          },
        },
        classes: true,
        _count: {
          select: {
            groups: true,
            classes: true,
          },
        },
      },
    })

    // Calculate KPIs
    const totalTeachers = teachers.length
    const totalGroups = teachers.reduce((sum, teacher) => sum + teacher._count.groups, 0)
    const totalClasses = teachers.reduce((sum, teacher) => sum + teacher._count.classes, 0)



    const totalStudents = teachers.reduce((sum, teacher) => {
      return sum + teacher.groups.reduce((groupSum, group) => {
        return groupSum + group.enrollments.length
      }, 0)
    }, 0)

    const kpis = {
      totalTeachers,
      totalGroups,
      totalClasses,
      totalStudents,
    }

    return NextResponse.json(kpis)
  } catch (error) {
    console.error('Error fetching teacher KPIs:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
