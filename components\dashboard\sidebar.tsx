'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { cn } from '@/lib/utils'
import {
  LayoutDashboard,
  Users,
  UserPlus,
  UserX,
  GraduationCap,
  BookOpen,
  CreditCard,
  BarChart3,
  Settings,
  Phone,
  UserCheck,
  Calendar,
  ClipboardList,
  ChevronDown,
  ChevronRight,
  Award,
  Target,
  TrendingUp,
  FileText,
  MessageSquare,
  Shield,
  Activity,
  PieChart,
  ClipboardCheck,
  Building
} from 'lucide-react'
import { useState } from 'react'
import { Badge } from '@/components/ui/badge'

// Define navigation categories and items with role-based access
interface NavigationItem {
  name: string
  href: string
  icon: any
  roles: string[]
  studentLevels?: string[] // For student progression
}

interface NavigationCategory {
  name: string
  items: NavigationItem[]
  roles: string[]
  collapsible?: boolean
}

const navigationConfig: NavigationCategory[] = [
  {
    name: 'Dashboard',
    roles: ['ADMI<PERSON>', '<PERSON>NAGE<PERSON>', 'RECEPTION', 'CASHIER'],
    items: [
      {
        name: 'Overview',
        href: '/dashboard',
        icon: LayoutDashboard,
        roles: ['ADMIN', 'MANAGER', 'RECEPTION', 'CASHIER']
      },
    ]
  },
  {
    name: 'Student Management',
    roles: ['ADMIN', 'MANAGER', 'RECEPTION'],
    collapsible: true,
    items: [
      { name: 'Leads', href: '/dashboard/leads', icon: UserPlus, roles: ['ADMIN', 'MANAGER', 'RECEPTION'] },
      { name: 'Students', href: '/dashboard/students', icon: Users, roles: ['ADMIN', 'MANAGER', 'RECEPTION', 'CASHIER'] },
    ]
  },
  {
    name: 'Academic Management',
    roles: ['ADMIN', 'MANAGER', 'RECEPTION'],
    collapsible: true,
    items: [
      { name: 'Teachers', href: '/dashboard/teachers', icon: UserCheck, roles: ['ADMIN', 'MANAGER'] },
      { name: 'Groups', href: '/dashboard/groups', icon: GraduationCap, roles: ['ADMIN', 'MANAGER', 'RECEPTION'] },
      { name: 'Cabinets', href: '/dashboard/cabinets', icon: Building, roles: ['ADMIN', 'MANAGER'] },
    ]
  },
  {
    name: 'Financial Management',
    roles: ['ADMIN', 'CASHIER'], // Removed MANAGER from financial access
    collapsible: true,
    items: [
      { name: 'Payments', href: '/dashboard/payments', icon: CreditCard, roles: ['ADMIN', 'CASHIER'] },
      { name: 'Analytics', href: '/dashboard/analytics', icon: BarChart3, roles: ['ADMIN'] }, // ADMIN ONLY
    ]
  },

  {
    name: 'Communication',
    roles: ['ADMIN', 'MANAGER', 'RECEPTION'],
    items: [
      { name: 'Messages', href: '/dashboard/communication', icon: MessageSquare, roles: ['ADMIN', 'MANAGER', 'RECEPTION'] },
      { name: 'Announcements', href: '/dashboard/communication/announcements', icon: Phone, roles: ['ADMIN', 'MANAGER', 'RECEPTION'] },
    ]
  },
  {
    name: 'Administration',
    roles: ['ADMIN', 'MANAGER'],
    collapsible: true,
    items: [
      { name: 'Users', href: '/dashboard/users', icon: Shield, roles: ['ADMIN'] },
      { name: 'Activity Logs', href: '/dashboard/admin/activity-logs', icon: Activity, roles: ['ADMIN'] },
      { name: 'KPI Dashboard', href: '/dashboard/admin/kpis', icon: PieChart, roles: ['ADMIN', 'MANAGER'] },

      { name: 'Teacher KPIs', href: '/dashboard/admin/teacher-kpis', icon: TrendingUp, roles: ['ADMIN', 'MANAGER'] },
      { name: 'Settings', href: '/dashboard/settings', icon: Settings, roles: ['ADMIN', 'MANAGER'] },
    ]
  }
]

// Helper function to get student level color
const getStudentLevelColor = (level: string) => {
  const colors: { [key: string]: string } = {
    'A1': 'bg-red-100 text-red-800',
    'A2': 'bg-orange-100 text-orange-800',
    'B1': 'bg-yellow-100 text-yellow-800',
    'B2': 'bg-green-100 text-green-800',
    'IELTS': 'bg-indigo-100 text-indigo-800',
    'SAT': 'bg-cyan-100 text-cyan-800',
    'MATH': 'bg-emerald-100 text-emerald-800',
    'KIDS': 'bg-pink-100 text-pink-800',
  }
  return colors[level] || 'bg-gray-100 text-gray-800'
}

// Helper function to get next level in progression
const getNextLevel = (currentLevel: string): string | null => {
  const progression = ['A1', 'A2', 'B1', 'B2']
  const currentIndex = progression.indexOf(currentLevel)
  return currentIndex !== -1 && currentIndex < progression.length - 1
    ? progression[currentIndex + 1]
    : null
}

export function Sidebar() {
  const pathname = usePathname()
  const { data: session } = useSession()
  const [collapsedSections, setCollapsedSections] = useState<string[]>([])

  const userRole = session?.user?.role as string
  const userName = session?.user?.name

  // Mock student level - in real implementation, this would come from the session or API
  const studentLevel = userRole === 'STUDENT' ? 'B1' : null
  const nextLevel = studentLevel ? getNextLevel(studentLevel) : null

  // Filter navigation based on user role
  const getFilteredNavigation = () => {
    if (!userRole) return []

    return navigationConfig.filter(category =>
      category.roles.includes(userRole)
    ).map(category => ({
      ...category,
      items: category.items.filter(item =>
        item.roles.includes(userRole)
      )
    })).filter(category => category.items.length > 0)
  }

  const toggleSection = (sectionName: string) => {
    setCollapsedSections(prev =>
      prev.includes(sectionName)
        ? prev.filter(name => name !== sectionName)
        : [...prev, sectionName]
    )
  }

  const filteredNavigation = getFilteredNavigation()

  return (
    <div className="flex flex-col w-72 bg-white shadow-xl h-full border-r border-gray-100">
      {/* Header */}
      <div className="flex items-center justify-center h-20 px-6 gradient-primary">
        <BookOpen className="h-8 w-8 text-white mr-3" />
        <span className="text-xl font-bold text-white tracking-tight">Innovative CRM</span>
      </div>

      {/* User Info Section */}
      {session?.user && (
        <div className="px-6 py-5 border-b border-gray-100 bg-white">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full gradient-primary flex items-center justify-center shadow-md">
                <span className="text-sm font-medium text-white">
                  {userName?.charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-semibold text-gray-900 truncate">
                {userName}
              </p>
              <div className="flex items-center space-x-2 mt-0.5">
                <p className="text-xs text-gray-500 capitalize font-medium">
                  {userRole?.toLowerCase()}
                </p>
                {studentLevel && (
                  <Badge className={cn('text-xs font-medium', getStudentLevelColor(studentLevel))}>
                    Level {studentLevel}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Student Progress Indicator */}
          {studentLevel && nextLevel && (
            <div className="mt-4 p-3 bg-white rounded-lg border border-gray-100 shadow-sm">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-700 font-medium">Progress to {nextLevel}</span>
                <Target className="h-3.5 w-3.5 text-blue-600" />
              </div>
              <div className="mt-2 w-full bg-gray-100 rounded-full h-2">
                <div className="gradient-primary h-2 rounded-full" style={{ width: '65%' }}></div>
              </div>
              <p className="text-xs text-gray-600 font-medium mt-1.5 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1 text-blue-600" />
                65% Complete
              </p>
            </div>
          )}
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 px-6 py-6 space-y-2 overflow-y-auto">
        {filteredNavigation.map((category) => {
          const isCollapsed = collapsedSections.includes(category.name)
          const showItems = !category.collapsible || !isCollapsed

          return (
            <div key={category.name} className="space-y-1.5">
              {/* Category Header */}
              <div className="flex items-center justify-between">
                {category.collapsible ? (
                  <button
                    onClick={() => toggleSection(category.name)}
                    className="flex items-center w-full px-3 py-2 text-xs font-bold text-gray-500 uppercase tracking-wider hover:text-gray-700 transition-all duration-200 rounded-lg hover:bg-gray-50"
                  >
                    <span className="flex-1 text-left">{category.name}</span>
                    {isCollapsed ? (
                      <ChevronRight className="h-3.5 w-3.5 text-gray-400" />
                    ) : (
                      <ChevronDown className="h-3.5 w-3.5 text-gray-400" />
                    )}
                  </button>
                ) : (
                  <h3 className="px-3 py-2 text-xs font-bold text-gray-500 uppercase tracking-wider">
                    {category.name}
                  </h3>
                )}
              </div>

              {/* Category Items */}
              {showItems && (
                <div className="space-y-1">
                  {category.items.map((item) => {
                    const isActive = pathname === item.href
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={cn(
                          'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group',
                          isActive
                            ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-600 shadow-sm'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'
                        )}
                      >
                        <item.icon className={cn(
                          'mr-3 h-5 w-5 transition-colors duration-200',
                          isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600'
                        )} />
                        <span className="truncate">{item.name}</span>
                      </Link>
                    )
                  })}
                </div>
              )}

              {/* Add spacing between categories */}
              {category !== filteredNavigation[filteredNavigation.length - 1] && (
                <div className="border-b border-gray-100 my-2"></div>
              )}
            </div>
          )
        })}
      </nav>
    </div>
  )
}
