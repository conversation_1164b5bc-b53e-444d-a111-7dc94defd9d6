import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const enrollmentSchema = z.object({
  studentId: z.string(),
  groupId: z.string(),
  status: z.enum(['ACTIVE', 'COMPLETED', 'DROPPED', 'SUSPENDED']).default('ACTIVE'),
  startDate: z.string(),
  endDate: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only ADMIN, MANAGER, and RECEPTION can view enrollments
    if (!session.user.role || !['ADMIN', 'MANAGER', 'RECEPTION'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const groupId = searchParams.get('groupId')
    const studentId = searchParams.get('studentId')
    const requestedBranch = searchParams.get('branch')

    // Determine branch based on user role
    const userRole = (session.user as any).role
    const userBranch = (session.user as any).branch
    let branchFilter: string

    if (userRole === 'ADMIN') {
      // ADMIN can view any branch or all branches
      branchFilter = requestedBranch || userBranch || 'main'
    } else {
      // Non-admin users can only see their assigned branch
      branchFilter = userBranch || 'main'
    }

    // Use branch ID directly for database query (data is stored with branch IDs, not names)
    const where: any = {
      // Filter enrollments by branch through both student and group relationships
      AND: [
        { student: { branch: branchFilter } },
        { group: { branch: branchFilter } }
      ]
    }

    if (search) {
      where.OR = [
        {
          student: {
            branch: branchFilter,
            user: { name: { contains: search, mode: 'insensitive' } }
          }
        },
        {
          student: {
            branch: branchFilter,
            user: { phone: { contains: search } }
          }
        },
        {
          group: {
            branch: branchFilter,
            name: { contains: search, mode: 'insensitive' }
          }
        },
        {
          group: {
            branch: branchFilter,
            course: { name: { contains: search, mode: 'insensitive' } }
          }
        },
      ]
    }

    if (status) {
      where.status = status
    }

    if (groupId) {
      where.groupId = groupId
      // Ensure the group belongs to the current branch
      where.AND.push({ group: { id: groupId, branch: branchFilter } })
    }

    if (studentId) {
      where.studentId = studentId
      // Ensure the student belongs to the current branch
      where.AND.push({ student: { id: studentId, branch: branchFilter } })
    }

    const [enrollments, total] = await Promise.all([
      prisma.enrollment.findMany({
        where,
        include: {
          student: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  phone: true,
                  email: true,
                },
              },
            },
          },
          group: {
            include: {
              course: {
                select: {
                  id: true,
                  name: true,
                  level: true,
                  duration: true,
                  price: true,
                },
              },
              teacher: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.enrollment.count({ where }),
    ])

    return NextResponse.json({
      enrollments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching enrollments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only ADMIN, MANAGER, and RECEPTION can create enrollments
    if (!session.user.role || !['ADMIN', 'MANAGER', 'RECEPTION'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = enrollmentSchema.parse(body)

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: validatedData.studentId },
    })

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 400 }
      )
    }

    // Check if group exists and has capacity
    const group = await prisma.group.findUnique({
      where: { id: validatedData.groupId },
      include: {
        _count: {
          select: {
            enrollments: {
              where: {
                status: 'ACTIVE',
              },
            },
          },
        },
      },
    })

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found' },
        { status: 400 }
      )
    }

    if (!group.isActive) {
      return NextResponse.json(
        { error: 'Cannot enroll in inactive group' },
        { status: 400 }
      )
    }

    // Check group capacity
    if (group._count.enrollments >= group.capacity) {
      return NextResponse.json(
        { error: 'Group is at full capacity' },
        { status: 400 }
      )
    }

    // Check if student is already enrolled in this group
    const existingEnrollment = await prisma.enrollment.findUnique({
      where: {
        studentId_groupId: {
          studentId: validatedData.studentId,
          groupId: validatedData.groupId,
        },
      },
    })

    if (existingEnrollment) {
      return NextResponse.json(
        { error: 'Student is already enrolled in this group' },
        { status: 400 }
      )
    }

    // Use transaction to create enrollment and update student's current group
    const enrollment = await prisma.$transaction(async (tx) => {
      // Create the enrollment
      const newEnrollment = await tx.enrollment.create({
        data: {
          ...validatedData,
          startDate: new Date(validatedData.startDate),
          endDate: validatedData.endDate ? new Date(validatedData.endDate) : null,
        },
      })

      // Update student's current group if enrollment is active
      if (validatedData.status === 'ACTIVE') {
        await tx.student.update({
          where: { id: validatedData.studentId },
          data: { currentGroupId: validatedData.groupId },
        })
      }

      return newEnrollment
    })

    // Fetch the complete enrollment data with relationships
    const completeEnrollment = await prisma.enrollment.findUnique({
      where: { id: enrollment.id },
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
          },
        },
        group: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    return NextResponse.json(completeEnrollment, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating enrollment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
