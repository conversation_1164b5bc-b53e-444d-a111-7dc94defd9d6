import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const branch = searchParams.get('branch')
    const search = searchParams.get('search')

    const where: any = {
      isActive: true,
      cabinetId: null, // Only groups without cabinet assignment
    }

    if (branch) {
      // Map branch ID to branch name for database query
      const branchName = branch === 'main' ? 'Main Branch' : 'Branch'
      where.branch = branchName
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { course: { name: { contains: search, mode: 'insensitive' } } },
        { teacher: { user: { name: { contains: search, mode: 'insensitive' } } } },
      ]
    }

    const unassignedGroups = await prisma.group.findMany({
      where,
      include: {
        course: {
          select: {
            name: true,
            level: true,
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
        _count: {
          select: {
            enrollments: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    return NextResponse.json({
      groups: unassignedGroups,
      total: unassignedGroups.length,
    })
  } catch (error) {
    console.error('Error fetching unassigned groups:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
