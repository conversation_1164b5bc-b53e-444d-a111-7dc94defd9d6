generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id        String   @id @default(cuid())
  email     String?  @unique
  phone     String   @unique
  name      String
  role      Role     @default(STUDENT)
  branch    String   @default("main") // Branch assignment for role-based access
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  accounts       Account[]
  sessions       Session[]
  studentProfile Student?
  teacherProfile Teacher?
  activityLogs   ActivityLog[]
  callRecords    CallRecord[]
  messages       Message[]
  announcements  Announcement[]
  notifications  Notification[]

  @@map("users")
}

model Student {
  id               String        @id @default(cuid())
  userId           String        @unique
  level            Level         @default(A1)
  branch           String
  emergencyContact String?
  photoUrl         String?
  dateOfBirth      DateTime?
  address          String?

  // Status tracking fields
  status           StudentStatus @default(ACTIVE)
  currentGroupId   String?
  droppedAt        DateTime?
  pausedAt         DateTime?
  resumedAt        DateTime?

  // Lead-like fields for dropped students
  reEnrollmentNotes String?
  lastContactedAt   DateTime?

  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  currentGroup Group?       @relation("StudentCurrentGroup", fields: [currentGroupId], references: [id])
  enrollments  Enrollment[] // Keep for historical data
  payments     Payment[]
  attendances  Attendance[]
  assessments  Assessment[]

  @@map("students")
}

model Teacher {
  id          String      @id @default(cuid())
  userId      String      @unique
  subject     String
  experience  Int?
  salary      Decimal?
  branch      String
  photoUrl    String?
  tier        TeacherTier @default(NEW)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  groups       Group[]
  classes      Class[]
  assignedLeads Lead[]

  @@map("teachers")
}

model Lead {
  id               String     @id @default(cuid())
  name             String
  phone            String     @unique
  coursePreference String
  status           LeadStatus @default(NEW)
  source           String?
  notes            String?
  branch           String     @default("main")
  assignedTo       String?
  followUpDate     DateTime?

  // Call management fields
  callStartedAt    DateTime?
  callEndedAt      DateTime?
  callDuration     Int?       // in seconds

  // Group assignment fields
  assignedGroupId  String?
  assignedTeacherId String?
  assignedAt       DateTime?

  // Archive management
  archivedAt       DateTime?

  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt

  // Relations
  assignedGroup    Group?     @relation(fields: [assignedGroupId], references: [id])
  assignedTeacher  Teacher?   @relation(fields: [assignedTeacherId], references: [id])
  callRecords      CallRecord[]

  @@map("leads")
}

model CallRecord {
  id          String   @id @default(cuid())
  leadId      String
  userId      String   // User who made the call
  startedAt   DateTime
  endedAt     DateTime?
  duration    Int?     // in seconds
  notes       String?
  recordingUrl String? // URL to call recording if available
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  lead        Lead     @relation(fields: [leadId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id])

  @@map("call_records")
}

model Course {
  id          String   @id @default(cuid())
  name        String   @unique
  level       Level
  description String?
  duration    Int // in weeks
  price       Decimal
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  groups Group[]

  @@map("courses")
}

model Group {
  id        String   @id @default(cuid())
  name      String   @unique
  courseId  String
  teacherId String
  capacity  Int      @default(20)
  schedule  String // JSON string for schedule
  room      String?
  cabinetId String?
  branch    String
  startDate DateTime
  endDate   DateTime
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  course         Course            @relation(fields: [courseId], references: [id])
  teacher        Teacher           @relation(fields: [teacherId], references: [id])
  cabinet        Cabinet?          @relation(fields: [cabinetId], references: [id])
  currentStudents Student[]        @relation("StudentCurrentGroup") // Direct student assignment
  enrollments    Enrollment[]      // Keep for historical data
  classes        Class[]
  assessments    Assessment[]
  assignedLeads  Lead[]
  cabinetSchedules CabinetSchedule[]

  @@map("groups")
}

model Cabinet {
  id          String   @id @default(cuid())
  name        String   @unique
  number      String   @unique
  capacity    Int      @default(20)
  floor       Int?
  building    String?
  branch      String
  equipment   String?  // JSON string for equipment list
  isActive    Boolean  @default(true)
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  groups      Group[]
  schedules   CabinetSchedule[]

  @@map("cabinets")
}

model CabinetSchedule {
  id        String   @id @default(cuid())
  cabinetId String
  groupId   String?
  dayOfWeek Int      // 0 = Sunday, 1 = Monday, etc.
  startTime String   // HH:MM format
  endTime   String   // HH:MM format
  title     String?
  isBlocked Boolean  @default(false) // For maintenance or other blocks
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  cabinet   Cabinet @relation(fields: [cabinetId], references: [id], onDelete: Cascade)
  group     Group?  @relation(fields: [groupId], references: [id], onDelete: SetNull)

  @@map("cabinet_schedules")
}

model Enrollment {
  id        String           @id @default(cuid())
  studentId String
  groupId   String
  status    EnrollmentStatus @default(ACTIVE)
  startDate DateTime
  endDate   DateTime?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Relations
  student Student @relation(fields: [studentId], references: [id])
  group   Group   @relation(fields: [groupId], references: [id])

  @@unique([studentId, groupId])
  @@map("enrollments")
}

model Class {
  id        String   @id @default(cuid())
  groupId   String
  teacherId String
  date      DateTime
  topic     String?
  homework  String?
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  group       Group        @relation(fields: [groupId], references: [id])
  teacher     Teacher      @relation(fields: [teacherId], references: [id])
  attendances Attendance[]

  @@map("classes")
}

model Attendance {
  id        String           @id @default(cuid())
  studentId String
  classId   String
  status    AttendanceStatus @default(PRESENT)
  notes     String?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Relations
  student Student @relation(fields: [studentId], references: [id])
  class   Class   @relation(fields: [classId], references: [id])

  @@unique([studentId, classId])
  @@map("attendances")
}

model Payment {
  id            String        @id @default(cuid())
  studentId     String
  amount        Decimal
  method        PaymentMethod
  status        PaymentStatus @default(PAID)
  description   String?
  transactionId String?
  dueDate       DateTime?
  paidDate      DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  student Student @relation(fields: [studentId], references: [id])

  @@map("payments")
}

model ActivityLog {
  id          String   @id @default(cuid())
  userId      String
  userRole    Role
  action      String   // e.g., "CREATE", "UPDATE", "DELETE", "VIEW"
  resource    String   // e.g., "student", "payment", "group"
  resourceId  String?  // ID of the affected resource
  details     Json?    // Additional details about the action
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("activity_logs")
}

model Assessment {
  id            String         @id @default(cuid())
  studentId     String?        // Null for group tests not yet assigned
  groupId       String?        // For group tests
  testName      String         // Name of the test that was administered
  type          AssessmentType
  level         Level?
  score         Int?
  maxScore      Int?
  passed        Boolean        @default(false)
  questions     Json?          // Store questions and answers
  results       Json?          // Store detailed results
  assignedBy    String?        // Teacher/Admin who assigned
  assignedAt    DateTime?      // When assigned to student/group
  startedAt     DateTime?      // When student started
  completedAt   DateTime?
  branch        String         @default("main") // Direct branch assignment for assessments
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  // Relations
  student   Student? @relation(fields: [studentId], references: [id])
  group     Group?   @relation(fields: [groupId], references: [id])

  @@map("assessments")
}

model Message {
  id            String        @id @default(cuid())
  subject       String
  content       String
  recipientType String        // 'ALL', 'STUDENTS', 'TEACHERS', 'ACADEMIC_MANAGERS', 'SPECIFIC'
  recipientIds  String[]      // Array of user IDs for specific recipients
  priority      String        @default("MEDIUM") // 'LOW', 'MEDIUM', 'HIGH'
  status        String        @default("DRAFT") // 'DRAFT', 'SENT', 'FAILED'
  sentAt        DateTime?
  senderId      String
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  sender        User          @relation(fields: [senderId], references: [id])

  @@map("messages")
}

model Announcement {
  id            String        @id @default(cuid())
  title         String
  content       String
  priority      String        @default("MEDIUM") // 'LOW', 'MEDIUM', 'HIGH'
  targetAudience String       @default("ALL") // 'ALL', 'STUDENTS', 'TEACHERS', 'ACADEMIC_MANAGERS'
  isActive      Boolean       @default(true)
  authorId      String
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  author        User          @relation(fields: [authorId], references: [id])

  @@map("announcements")
}

model Notification {
  id          String              @id @default(cuid())
  title       String
  message     String
  type        NotificationType    @default(INFO)
  priority    NotificationPriority @default(MEDIUM)
  read        Boolean             @default(false)
  actionUrl   String?
  userId      String
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt

  // Relations
  user        User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}



enum Role {
  ADMIN
  MANAGER
  TEACHER
  RECEPTION
  CASHIER
  STUDENT
  ACADEMIC_MANAGER
}

enum Level {
  A1
  A2
  B1
  B2
  IELTS
  SAT
  MATH
  KIDS
}

enum LeadStatus {
  NEW
  CALLING
  CALL_COMPLETED
  GROUP_ASSIGNED
  ARCHIVED
  NOT_INTERESTED
}

enum StudentStatus {
  ACTIVE
  DROPPED
  PAUSED
  COMPLETED
}

enum EnrollmentStatus {
  ACTIVE
  COMPLETED
  DROPPED
  SUSPENDED
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum PaymentMethod {
  CASH
  CARD
}

enum PaymentStatus {
  PAID
  DEBT
  REFUNDED
}

enum AssessmentType {
  LEVEL_TEST
  PROGRESS_TEST
  FINAL_EXAM
  GROUP_TEST
}

enum AssessmentStatus {
  DRAFT
  ASSIGNED
  IN_PROGRESS
  COMPLETED
  EXPIRED
}

enum TeacherTier {
  A_LEVEL
  B_LEVEL
  C_LEVEL
  NEW
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  ENROLLMENT
  PAYMENT
  REMINDER
  COMPLETION
  ATTENDANCE
}

enum NotificationPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}
