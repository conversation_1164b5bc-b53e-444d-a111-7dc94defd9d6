@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Professional CRM Color Palette */
    --background: oklch(1 0 0);
    --foreground: oklch(0.141 0.005 285.823);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.141 0.005 285.823);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.141 0.005 285.823);

    /* Primary: Professional Blue */
    --primary: oklch(0.596 0.145 163.225);
    --primary-foreground: oklch(0.979 0.021 166.113);

    /* Secondary: Sophisticated Gray */
    --secondary: oklch(0.967 0.001 286.375);
    --secondary-foreground: oklch(0.21 0.006 285.885);

    /* Accent: Modern Teal */
    --accent: oklch(0.967 0.001 286.375);
    --accent-foreground: oklch(0.21 0.006 285.885);

    /* Muted: Elegant <PERSON> */
    --muted: oklch(0.967 0.001 286.375);
    --muted-foreground: oklch(0.552 0.016 285.938);

    /* Success: Professional Green */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    /* Warning: Attention Orange */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    /* Destructive: Alert Red */
    --destructive: oklch(0.637 0.237 25.331);
    --destructive-foreground: oklch(0.637 0.237 25.331);

    /* UI Elements */
    --border: oklch(0.92 0.004 286.32);
    --input: oklch(0.871 0.006 286.286);
    --ring: oklch(0.871 0.006 286.286);
    --radius: 0.625rem;

    /* Dashboard Specific */
    --dashboard-bg: 248 250% 99%;
    --sidebar-bg: 222.2 84% 4.9%;
    --sidebar-hover: 217.2 32.6% 17.5%;
    --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --card-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.37 0.013 285.805);
    --sidebar-primary: oklch(0.21 0.006 285.885);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.967 0.001 286.375);
    --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
    --sidebar-border: oklch(0.92 0.004 286.32);
    --sidebar-ring: oklch(0.871 0.006 286.286);

    /* Sidebar Layout Variables */
    --sidebar-width: 16rem;
    --sidebar-width-icon: 3rem;
    --sidebar-width-mobile: 18rem;
  }

  .dark {
    --background: oklch(0.21 0.006 285.885);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.21 0.006 285.885);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.21 0.006 285.885);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.596 0.145 163.225);
    --primary-foreground: oklch(0.979 0.021 166.113);
    --secondary: oklch(0.274 0.006 286.033);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.244 0.006 285.97);
    --muted-foreground: oklch(0.705 0.015 286.067);
    --accent: oklch(0.244 0.006 285.97);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.637 0.237 25.331);
    --destructive-foreground: oklch(0.637 0.237 25.331);
    --border: oklch(0.29 0.009 285.83);
    --input: oklch(0.29 0.009 285.83);
    --ring: oklch(0.442 0.017 285.786);
    --dashboard-bg: 222.2 84% 4.9%;
    --sidebar-bg: 222.2 84% 4.9%;
    --sidebar-hover: 217.2 32.6% 17.5%;
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.244 0.006 285.97);
    --sidebar-foreground: oklch(0.967 0.001 286.375);
    --sidebar-primary: oklch(0.596 0.145 163.225);
    --sidebar-primary-foreground: oklch(1 0 0);
    --sidebar-accent: oklch(0.274 0.006 286.033);
    --sidebar-accent-foreground: oklch(0.967 0.001 286.375);
    --sidebar-border: oklch(0.274 0.006 286.033);
    --sidebar-ring: oklch(0.442 0.017 285.786);

    /* Sidebar Layout Variables */
    --sidebar-width: 16rem;
    --sidebar-width-icon: 3rem;
    --sidebar-width-mobile: 18rem;
  }
  .theme {
    --font-sans: var(--font-sans), ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
}

@layer components {
  /* Origin UI Compatible Components */

  /* Sidebar Layout Fix */
  .sidebar-layout {
    display: flex;
    min-height: 100vh;
  }

  .sidebar-content {
    flex: 1;
    min-width: 0;
  }











  /* Professional Animations */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

@layer utilities {
  /* Origin UI Compatible Utilities */

  /* Professional Shadows */
  .shadow-professional {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .shadow-professional-lg {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  /* Professional Text */
  .text-professional {
    @apply text-gray-700 leading-relaxed;
  }

  .text-professional-heading {
    @apply text-gray-900 font-semibold tracking-tight;
  }
}

/* Professional Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Professional Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
